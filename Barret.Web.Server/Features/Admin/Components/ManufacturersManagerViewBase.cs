using Barret.Services.Core.Areas.Manufacturers;
using Barret.Shared.DTOs.Devices;
using Radzen.Blazor;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Barret.Web.Server.Services;

namespace Barret.Web.Server.Features.Admin.Components
{
    /// <summary>
    /// Base class for the manufacturers manager view.
    /// </summary>
    public class ManufacturersManagerViewBase : ComponentBase
    {
        /// <summary>
        /// Gets or sets the title of the view.
        /// </summary>
        [Parameter]
        public string Title { get; set; } = "Manufacturers & Models";

        /// <summary>
        /// Gets or sets the manufacturer service.
        /// </summary>
        [Inject]
        protected IManufacturerService ManufacturerService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the logger.
        /// </summary>
        [Inject]
        protected ILogger<ManufacturersManagerViewBase> Logger { get; set; } = null!;

        /// <summary>
        /// Gets or sets the toast notification service.
        /// </summary>
        [Inject]
        protected IBarretToastService ToastService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the grid reference.
        /// </summary>
        protected RadzenDataGrid<ManufacturerInfo> Grid { get; set; } = null!;

        /// <summary>
        /// Gets or sets a value indicating whether the component is loading.
        /// </summary>
        protected bool IsLoading { get; set; } = true;

        /// <summary>
        /// Gets or sets the list of manufacturers.
        /// </summary>
        protected List<ManufacturerInfo> Manufacturers { get; set; } = new();



        // Dialog properties
        protected bool isManufacturerDialogVisible = false;
        protected string manufacturerDialogTitle = "Add Manufacturer";
        protected string manufacturerName = string.Empty;
        protected string manufacturerNameError = string.Empty;
        protected ManufacturerInfo? manufacturerToEdit;

        // Delete confirmation properties
        protected bool isDeleteConfirmationVisible = false;
        protected ManufacturerInfo? manufacturerToDelete;

        /// <summary>
        /// Initializes the component.
        /// </summary>
        /// <returns>A task that represents the asynchronous operation.</returns>
        protected override async Task OnInitializedAsync()
        {
            await LoadManufacturersAsync();
        }

        /// <summary>
        /// Loads the manufacturers from the service.
        /// </summary>
        /// <returns>A task that represents the asynchronous operation.</returns>
        protected async Task LoadManufacturersAsync()
        {
            try
            {
                IsLoading = true;
                StateHasChanged();

                var result = await ManufacturerService.GetAllManufacturersAsync();
                if (result.Success)
                {
                    Manufacturers = result.Data.ToList();
                }
                else
                {
                    ToastService.ShowToast("Error", $"Failed to load manufacturers: {result.ErrorMessage}", ToastType.Error);
                    Logger.LogError("Failed to load manufacturers: {ErrorMessage}", result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                ToastService.ShowToast("Error", $"An error occurred: {ex.Message}", ToastType.Error);
                Logger.LogError(ex, "Error loading manufacturers");
            }
            finally
            {
                IsLoading = false;
                StateHasChanged();
            }
        }



        /// <summary>
        /// Opens the add manufacturer dialog.
        /// </summary>
        protected void OpenAddManufacturerDialog()
        {
            manufacturerDialogTitle = "Add Manufacturer";
            manufacturerName = string.Empty;
            manufacturerNameError = string.Empty;
            manufacturerToEdit = null;
            isManufacturerDialogVisible = true;
        }

        /// <summary>
        /// Opens the edit manufacturer dialog.
        /// </summary>
        /// <param name="manufacturer">The manufacturer to edit.</param>
        protected void EditManufacturer(ManufacturerInfo manufacturer)
        {
            manufacturerDialogTitle = "Edit Manufacturer";
            manufacturerName = manufacturer.Name;
            manufacturerNameError = string.Empty;
            manufacturerToEdit = manufacturer;
            isManufacturerDialogVisible = true;
        }

        /// <summary>
        /// Opens the delete manufacturer confirmation dialog.
        /// </summary>
        /// <param name="manufacturer">The manufacturer to delete.</param>
        protected void DeleteManufacturer(ManufacturerInfo manufacturer)
        {
            manufacturerToDelete = manufacturer;
            isDeleteConfirmationVisible = true;
        }

        /// <summary>
        /// Closes the manufacturer dialog.
        /// </summary>
        protected void CloseManufacturerDialog()
        {
            isManufacturerDialogVisible = false;
        }

        /// <summary>
        /// Saves the manufacturer.
        /// </summary>
        /// <returns>A task that represents the asynchronous operation.</returns>
        protected async Task SaveManufacturer()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(manufacturerName))
                {
                    manufacturerNameError = "Manufacturer name is required";
                    return;
                }

                manufacturerNameError = string.Empty;

                if (manufacturerToEdit == null)
                {
                    // Create new manufacturer
                    var result = await ManufacturerService.CreateManufacturerAsync(manufacturerName);
                    if (result.Success)
                    {
                        ToastService.ShowToast("Success", "Manufacturer created successfully", ToastType.Success);
                        await LoadManufacturersAsync();
                        isManufacturerDialogVisible = false;
                    }
                    else
                    {
                        ToastService.ShowToast("Error", $"Failed to create manufacturer: {result.ErrorMessage}", ToastType.Error);
                        Logger.LogError("Failed to create manufacturer: {ErrorMessage}", result.ErrorMessage);
                    }
                }
                else
                {
                    // Update existing manufacturer
                    var result = await ManufacturerService.UpdateManufacturerAsync(manufacturerToEdit.Id, manufacturerName);
                    if (result.Success)
                    {
                        ToastService.ShowToast("Success", "Manufacturer updated successfully", ToastType.Success);
                        await LoadManufacturersAsync();
                        isManufacturerDialogVisible = false;
                    }
                    else
                    {
                        ToastService.ShowToast("Error", $"Failed to update manufacturer: {result.ErrorMessage}", ToastType.Error);
                        Logger.LogError("Failed to update manufacturer: {ErrorMessage}", result.ErrorMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                ToastService.ShowToast("Error", $"An error occurred: {ex.Message}", ToastType.Error);
                Logger.LogError(ex, "Error saving manufacturer");
            }
        }

        /// <summary>
        /// Confirms the deletion of a manufacturer.
        /// </summary>
        /// <returns>A task that represents the asynchronous operation.</returns>
        protected async Task ConfirmDeleteManufacturer()
        {
            try
            {
                if (manufacturerToDelete == null)
                {
                    ToastService.ShowToast("Error", "No manufacturer selected for deletion", ToastType.Error);
                    return;
                }

                var result = await ManufacturerService.DeleteManufacturerAsync(manufacturerToDelete.Id);
                if (result.Success)
                {
                    ToastService.ShowToast("Success", "Manufacturer deleted successfully", ToastType.Success);
                    await LoadManufacturersAsync();
                    isDeleteConfirmationVisible = false;
                }
                else
                {
                    ToastService.ShowToast("Error", $"Failed to delete manufacturer: {result.ErrorMessage}", ToastType.Error);
                    Logger.LogError("Failed to delete manufacturer: {ErrorMessage}", result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                ToastService.ShowToast("Error", $"An error occurred: {ex.Message}", ToastType.Error);
                Logger.LogError(ex, "Error deleting manufacturer");
            }
        }
    }
}
