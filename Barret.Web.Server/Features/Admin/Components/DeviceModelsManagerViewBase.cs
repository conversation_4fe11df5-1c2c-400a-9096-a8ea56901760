using Barret.Core.Areas.Devices.Enums;
using Barret.Services.Core.Areas.DeviceModels.Queries;
using Barret.Services.Core.Areas.Manufacturers;
using Barret.Shared.DTOs.Devices;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Barret.Web.Server.Services;
using Radzen.Blazor;

namespace Barret.Web.Server.Features.Admin.Components
{
    /// <summary>
    /// Base class for the device models manager view.
    /// </summary>
    public class DeviceModelsManagerViewBase : ComponentBase
    {
        /// <summary>
        /// Gets or sets the manufacturer ID.
        /// </summary>
        [Parameter]
        public Guid ManufacturerId { get; set; }

        /// <summary>
        /// Gets or sets the manufacturer name.
        /// </summary>
        [Parameter]
        public string ManufacturerName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the manufacturer service.
        /// </summary>
        [Inject]
        protected IManufacturerService ManufacturerService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the device model query service.
        /// </summary>
        [Inject]
        protected IDeviceModelQueryService DeviceModelQueryService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the logger.
        /// </summary>
        [Inject]
        protected ILogger<DeviceModelsManagerViewBase> Logger { get; set; } = null!;

        /// <summary>
        /// Gets or sets the toast notification service.
        /// </summary>
        [Inject]
        protected IBarretToastService ToastService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the device models grid reference.
        /// </summary>
        protected RadzenDataGrid<DeviceModelInfo> DeviceModelsGrid { get; set; } = null!;

        /// <summary>
        /// Gets or sets a value indicating whether the component is loading.
        /// </summary>
        protected bool IsLoading { get; set; } = true;

        /// <summary>
        /// Gets or sets the list of device models.
        /// </summary>
        protected List<DeviceModelInfo> DeviceModels { get; set; } = new();

        /// <summary>
        /// Gets the list of device roles.
        /// </summary>
        protected DeviceRole[] DeviceRoles => Enum.GetValues<DeviceRole>();

        // Dialog properties
        protected bool isDeviceModelDialogVisible = false;
        protected string deviceModelDialogTitle = "Add Device Model";
        protected string deviceModelName = string.Empty;
        protected string deviceModelNameError = string.Empty;
        protected DeviceRole? selectedDeviceRole = null;
        protected string deviceRoleError = string.Empty;
        protected DeviceModelInfo? deviceModelToEdit;

        // Delete confirmation properties
        protected bool isDeleteConfirmationVisible = false;
        protected DeviceModelInfo? deviceModelToDelete;

        /// <summary>
        /// Method invoked when the component is initialized.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        protected override async Task OnInitializedAsync()
        {
            await LoadDeviceModelsAsync();
        }

        /// <summary>
        /// Method invoked when the component parameters are set.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        protected override async Task OnParametersSetAsync()
        {
            await LoadDeviceModelsAsync();
        }

        /// <summary>
        /// Loads the device models from the service.
        /// </summary>
        /// <returns>A task that represents the asynchronous operation.</returns>
        protected async Task LoadDeviceModelsAsync()
        {
            try
            {
                if (ManufacturerId == Guid.Empty)
                {
                    DeviceModels = new List<DeviceModelInfo>();
                    IsLoading = false;
                    return;
                }

                IsLoading = true;
                StateHasChanged();

                try
                {
                    DeviceModels = await DeviceModelQueryService.GetDeviceModelsByManufacturerIdAsync(ManufacturerId);
                    Logger.LogInformation("Successfully loaded {Count} device models for manufacturer {ManufacturerId}", DeviceModels.Count, ManufacturerId);
                }
                catch (Exception ex)
                {
                    ToastService.ShowToast("Error", $"Failed to load device models: {ex.Message}", ToastType.Error);
                    Logger.LogError(ex, "Failed to load device models for manufacturer {ManufacturerId}", ManufacturerId);
                }
            }
            catch (Exception ex)
            {
                ToastService.ShowToast("Error", $"An error occurred: {ex.Message}", ToastType.Error);
                Logger.LogError(ex, "Error loading device models");
            }
            finally
            {
                IsLoading = false;
                StateHasChanged();
            }
        }

        /// <summary>
        /// Opens the add device model dialog.
        /// </summary>
        protected void OpenAddDeviceModelDialog()
        {
            deviceModelDialogTitle = "Add Device Model";
            deviceModelName = string.Empty;
            deviceModelNameError = string.Empty;
            selectedDeviceRole = null;
            deviceRoleError = string.Empty;
            deviceModelToEdit = null;
            isDeviceModelDialogVisible = true;
        }

        /// <summary>
        /// Opens the edit device model dialog.
        /// </summary>
        /// <param name="deviceModel">The device model to edit.</param>
        protected void EditDeviceModel(DeviceModelInfo deviceModel)
        {
            deviceModelDialogTitle = "Edit Device Model";
            deviceModelName = deviceModel.Name;
            deviceModelNameError = string.Empty;
            selectedDeviceRole = deviceModel.DeviceRole;
            deviceRoleError = string.Empty;
            deviceModelToEdit = deviceModel;
            isDeviceModelDialogVisible = true;
        }

        /// <summary>
        /// Opens the delete device model confirmation dialog.
        /// </summary>
        /// <param name="deviceModel">The device model to delete.</param>
        protected void DeleteDeviceModel(DeviceModelInfo deviceModel)
        {
            deviceModelToDelete = deviceModel;
            isDeleteConfirmationVisible = true;
        }

        /// <summary>
        /// Closes the device model dialog.
        /// </summary>
        protected void CloseDeviceModelDialog()
        {
            isDeviceModelDialogVisible = false;
        }

        /// <summary>
        /// Saves the device model.
        /// </summary>
        /// <returns>A task that represents the asynchronous operation.</returns>
        protected async Task SaveDeviceModel()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(deviceModelName))
                {
                    deviceModelNameError = "Model name is required";
                    return;
                }

                if (selectedDeviceRole == null)
                {
                    deviceRoleError = "Device role is required";
                    return;
                }

                deviceModelNameError = string.Empty;
                deviceRoleError = string.Empty;

                if (deviceModelToEdit == null)
                {
                    // Create new device model
                    try
                    {
                        var result = await ManufacturerService.AddDeviceModelAsync(ManufacturerId, deviceModelName, selectedDeviceRole.Value);
                        if (result.Success)
                        {
                            ToastService.ShowToast("Success", "Device model created successfully", ToastType.Success);
                            await LoadDeviceModelsAsync();
                            isDeviceModelDialogVisible = false;
                        }
                        else
                        {
                            ToastService.ShowToast("Error", $"Failed to create device model: {result.ErrorMessage}", ToastType.Error);
                            Logger.LogError("Failed to create device model: {ErrorMessage}", result.ErrorMessage);
                        }
                    }
                    catch (Exception ex)
                    {
                        ToastService.ShowToast("Error", $"Failed to create device model: {ex.Message}", ToastType.Error);
                        Logger.LogError(ex, "Error creating device model");
                    }
                }
                else
                {
                    // Update existing device model
                    try
                    {
                        // Use the UpdateDeviceModelAsync method to update the model
                        var updateResult = await ManufacturerService.UpdateDeviceModelAsync(deviceModelToEdit.Id, deviceModelName);
                        if (updateResult.Success)
                        {
                            ToastService.ShowToast("Success", "Device model updated successfully", ToastType.Success);
                            await LoadDeviceModelsAsync();
                            isDeviceModelDialogVisible = false;
                        }
                        else
                        {
                            ToastService.ShowToast("Error", $"Failed to update device model: {updateResult.ErrorMessage}", ToastType.Error);
                            Logger.LogError("Failed to update device model: {ErrorMessage}", updateResult.ErrorMessage);
                        }
                    }
                    catch (Exception ex)
                    {
                        ToastService.ShowToast("Error", $"Failed to update device model: {ex.Message}", ToastType.Error);
                        Logger.LogError(ex, "Error updating device model");
                    }
                }
            }
            catch (Exception ex)
            {
                ToastService.ShowToast("Error", $"An error occurred: {ex.Message}", ToastType.Error);
                Logger.LogError(ex, "Error saving device model");
            }
        }

        /// <summary>
        /// Confirms the deletion of a device model.
        /// </summary>
        /// <returns>A task that represents the asynchronous operation.</returns>
        protected async Task ConfirmDeleteDeviceModel()
        {
            try
            {
                if (deviceModelToDelete == null)
                {
                    ToastService.ShowToast("Error", "No device model selected for deletion", ToastType.Error);
                    return;
                }

                try
                {
                    var result = await ManufacturerService.RemoveDeviceModelFromManufacturerAsync(ManufacturerId, deviceModelToDelete.Id);
                    if (result.Success)
                    {
                        ToastService.ShowToast("Success", "Device model deleted successfully", ToastType.Success);
                        await LoadDeviceModelsAsync();
                        isDeleteConfirmationVisible = false;
                    }
                    else
                    {
                        ToastService.ShowToast("Error", $"Failed to delete device model: {result.ErrorMessage}", ToastType.Error);
                        Logger.LogError("Failed to delete device model: {ErrorMessage}", result.ErrorMessage);
                    }
                }
                catch (Exception ex)
                {
                    ToastService.ShowToast("Error", $"Failed to delete device model: {ex.Message}", ToastType.Error);
                    Logger.LogError(ex, "Error deleting device model");
                }
            }
            catch (Exception ex)
            {
                ToastService.ShowToast("Error", $"An error occurred: {ex.Message}", ToastType.Error);
                Logger.LogError(ex, "Error deleting device model");
            }
        }
    }
}
