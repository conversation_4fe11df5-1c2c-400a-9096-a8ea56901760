using Barret.Web.Server.Features.Shared;
using Barret.Web.Server.Features.Vehicles.Data;
using Barret.Web.Server.Features.Vehicles.List.ViewModels;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.Logging;
using <PERSON><PERSON>zen;
using Radzen.Blazor;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Barret.Web.Server.Features.Vehicles.List.Views
{
    /// <summary>
    /// Base class for the VehicleListView component.
    /// </summary>
    public class VehicleListViewBase : ViewBase<VehicleListViewModel>
    {
        /// <summary>
        /// Gets or sets the VehicleListViewModel factory.
        /// </summary>
        [Inject]
        protected IVehicleListViewModelFactory ViewModelFactory { get; set; } = null!;

        /// <summary>
        /// Gets or sets the navigation manager.
        /// </summary>
        [Inject]
        protected NavigationManager NavigationManager { get; set; } = null!;

        /// <summary>
        /// Gets or sets the dialog service.
        /// </summary>
        [Inject]
        protected DialogService DialogService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the vehicle type from the query string.
        /// </summary>
        [Parameter]
        [SupplyParameterFromQuery(Name = "type")]
        public string? VehicleType { get; set; }

        /// <summary>
        /// Gets or sets the search term from the query string.
        /// </summary>
        [Parameter]
        [SupplyParameterFromQuery(Name = "search")]
        public string? SearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the active tab index.
        /// </summary>
        protected int activeTabIndex = 0;

        /// <summary>
        /// Gets or sets the ID of the vehicle to delete.
        /// </summary>
        protected Guid vehicleToDeleteId;

        /// <summary>
        /// Gets or sets whether the copy dialog is visible.
        /// </summary>
        protected bool showCopyDialog = false;

        /// <summary>
        /// Gets or sets the ID of the vehicle to copy.
        /// </summary>
        protected Guid vehicleToCopyId;

        /// <summary>
        /// Gets or sets the name of the vehicle to copy.
        /// </summary>
        protected string vehicleToCopyName = string.Empty;

        /// <summary>
        /// Flag to track if vehicles have been loaded to prevent duplicate loads.
        /// </summary>
        private bool _initialLoadCompleted = false;

        /// <summary>
        /// Flag to track if we're currently in a loading operation to prevent concurrent loads.
        /// </summary>
        private bool _isLoadingInProgress = false;

        /// <summary>
        /// Timestamp of the last load operation to prevent rapid duplicate loads.
        /// </summary>
        private DateTime _lastLoadTime = DateTime.MinValue;

        /// <summary>
        /// Reference to the RadzenDataGrid component.
        /// </summary>
        protected RadzenDataGrid<VehicleData>? vehicleGrid;

        /// <summary>
        /// Method called when the component is initialized.
        /// </summary>
        protected override void OnInitialized()
        {
            // Create the ViewModel if it doesn't exist
            ViewModel ??= ViewModelFactory.Create();

            base.OnInitialized();
        }

        /// <summary>
        /// Method called after the component is initialized.
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            await base.OnInitializedAsync();

            // Set the filter from query parameters
            UpdateFilterFromQueryParameters();

            Logger.LogDebug("VehicleListViewBase initialized with filter: {VehicleType}, {SearchTerm}",
                ViewModel.Filter.VehicleType, ViewModel.Filter.SearchTerm);

            // Reset the load tracking flag on each initialization
            _initialLoadCompleted = false;
            _isLoadingInProgress = false;
        }

        /// <summary>
        /// Method called when the component parameters are set.
        /// </summary>
        protected override async Task OnParametersSetAsync()
        {
            await base.OnParametersSetAsync();

            // Check if parameters have changed
            // Normalize both current filter and parameter for comparison
            // When VehicleType parameter is null/empty, it should be treated as "All"
            string currentVehicleTypeString = ViewModel.Filter.VehicleType?.ToString() ?? "All";
            string parameterVehicleTypeString = string.IsNullOrEmpty(VehicleType) ? "All" : VehicleType;

            bool vehicleTypeChanged = !string.Equals(currentVehicleTypeString, parameterVehicleTypeString, StringComparison.OrdinalIgnoreCase);
            bool searchTermChanged = ViewModel.Filter.SearchTerm != SearchTerm;
            bool parametersChanged = vehicleTypeChanged || searchTermChanged;

            Logger.LogDebug("Parameter change check: VehicleType '{Current}' -> '{Parameter}' (Changed: {VehicleTypeChanged}), SearchTerm '{CurrentSearch}' -> '{ParameterSearch}' (Changed: {SearchChanged})",
                currentVehicleTypeString, parameterVehicleTypeString, vehicleTypeChanged,
                ViewModel.Filter.SearchTerm ?? "null", SearchTerm ?? "null", searchTermChanged);

            if (parametersChanged)
            {
                // Parameters changed, update filter
                UpdateFilterFromQueryParameters();
            }

            // Prevent loading if:
            // 1. We're already in the middle of a loading operation
            // 2. We've already completed the initial load and parameters haven't changed
            // 3. We've loaded within the last 500ms (to prevent duplicate loads during prerendering)
            bool shouldSkipLoading = _isLoadingInProgress ||
                                    (_initialLoadCompleted && !parametersChanged) ||
                                    (DateTime.Now - _lastLoadTime).TotalMilliseconds < 500;

            if (shouldSkipLoading)
            {
                Logger.LogDebug("Skipping vehicle load: isLoading={IsLoading}, initialLoadCompleted={InitialLoadCompleted}, " +
                               "parametersChanged={ParametersChanged}, timeSinceLastLoad={TimeSinceLastLoad}ms",
                               _isLoadingInProgress, _initialLoadCompleted, parametersChanged,
                               (DateTime.Now - _lastLoadTime).TotalMilliseconds);
                return;
            }

            try
            {
                // Set loading flags
                _isLoadingInProgress = true;
                _lastLoadTime = DateTime.Now;

                Logger.LogDebug("Loading vehicles in OnParametersSetAsync: initialLoad={InitialLoad}, parametersChanged={ParamsChanged}",
                    !_initialLoadCompleted, parametersChanged);

                await ViewModel.LoadVehiclesAsync();
                _initialLoadCompleted = true;
            }
            finally
            {
                _isLoadingInProgress = false;
            }
        }

        /// <summary>
        /// Method called after the component is rendered.
        /// </summary>
        protected override Task OnAfterRenderAsync(bool firstRender)
        {
            // We don't need to load data here, but log for debugging
            if (firstRender)
            {
                Logger.LogDebug("VehicleListView first render complete");
            }

            return base.OnAfterRenderAsync(firstRender);
        }

        /// <summary>
        /// Updates the filter from query parameters.
        /// </summary>
        private void UpdateFilterFromQueryParameters()
        {
            Logger.LogDebug("UpdateFilterFromQueryParameters called. VehicleType parameter: '{VehicleType}', SearchTerm parameter: '{SearchTerm}'",
                VehicleType ?? "null", SearchTerm ?? "null");

            // Set the vehicle type filter
            if (!string.IsNullOrEmpty(VehicleType) && Enum.TryParse<VehicleTypeEnum>(VehicleType, true, out var vehicleTypeEnum))
            {
                Logger.LogDebug("Setting filter VehicleType to: {VehicleType} (tab index: {TabIndex})", vehicleTypeEnum, (int)vehicleTypeEnum);
                ViewModel.Filter.VehicleType = vehicleTypeEnum;

                // Set the active tab index based on the vehicle type
                activeTabIndex = (int)vehicleTypeEnum;
            }
            else
            {
                Logger.LogDebug("Setting filter VehicleType to: All (tab index: 0)");
                ViewModel.Filter.VehicleType = VehicleTypeEnum.All;
                activeTabIndex = 0;
            }

            // Set the search term filter
            ViewModel.Filter.SearchTerm = SearchTerm;

            Logger.LogDebug("Filter updated. Final state: VehicleType={VehicleType}, SearchTerm='{SearchTerm}', ActiveTabIndex={ActiveTabIndex}",
                ViewModel.Filter.VehicleType, ViewModel.Filter.SearchTerm ?? "null", activeTabIndex);
        }

        /// <summary>
        /// Navigates to the vehicle configuration page.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle to configure.</param>
        protected void NavigateToVehicleConfiguration(Guid vehicleId)
        {
            NavigationManager.NavigateTo($"/vehicles/edit/{vehicleId}");
        }

        /// <summary>
        /// Navigates to the create new vehicle page.
        /// </summary>
        protected void NavigateToCreateNew()
        {
            NavigationManager.NavigateTo("/vehicles/create");
        }

        /// <summary>
        /// Handles the tab change event.
        /// </summary>
        /// <param name="tabIndex">The index of the selected tab.</param>
        protected async Task HandleTabChangeAsync(int tabIndex)
        {
            Logger.LogDebug("Tab changed to {TabIndex}", tabIndex);
            activeTabIndex = tabIndex;

            // Convert tab index to vehicle type for URL parameter
            var vehicleType = (VehicleTypeEnum)tabIndex;

            // Update the URL to reflect the filter
            // Don't update ViewModel.Filter here - let OnParametersSetAsync handle it
            string typeParam = tabIndex == 0 ? "" : $"?type={vehicleType}";
            string searchParam = !string.IsNullOrEmpty(SearchTerm) ?
                (typeParam.Length > 0 ? $"&search={SearchTerm}" : $"?search={SearchTerm}") : "";

            Logger.LogDebug("Navigating to URL with vehicle type: {VehicleType}", vehicleType);

            // Note: We're using NavigateTo with forceLoad=false, which means the component won't be
            // recreated, but OnParametersSetAsync will be called due to the URL change.
            // The loading will happen there based on parameter changes.
            NavigationManager.NavigateTo($"/vehicles{typeParam}{searchParam}", false);

            // We don't need to manually load vehicles here as OnParametersSetAsync will handle it
            // when the URL parameters change. This prevents duplicate loading.
        }

        /// <summary>
        /// Handles the search event.
        /// </summary>
        /// <param name="searchText">The search text.</param>
        protected async Task HandleSearchAsync(string searchText)
        {
            Logger.LogDebug("Search term changed to {SearchTerm}", searchText);
            ViewModel.Filter.SearchTerm = searchText;

            // Update the URL to reflect the filter
            string typeParam = ViewModel.Filter.VehicleType.HasValue && ViewModel.Filter.VehicleType.Value != VehicleTypeEnum.All ?
                $"?type={ViewModel.Filter.VehicleType}" : "";
            string searchParam = !string.IsNullOrEmpty(searchText) ?
                (typeParam.Length > 0 ? $"&search={searchText}" : $"?search={searchText}") : "";

            // Note: We're using NavigateTo with forceLoad=false, which means the component won't be
            // recreated, but OnParametersSetAsync will be called due to the URL change.
            // The loading will happen there based on parameter changes.
            NavigationManager.NavigateTo($"/vehicles{typeParam}{searchParam}", false);

            // We don't need to manually load vehicles here as OnParametersSetAsync will handle it
            // when the URL parameters change. This prevents duplicate loading.
        }

        /// <summary>
        /// Handles the key up event for the search input.
        /// </summary>
        /// <param name="e">The keyboard event args.</param>
        protected async Task HandleKeyUp(KeyboardEventArgs e)
        {
            if (e.Key == "Enter")
            {
                await HandleSearchAsync(ViewModel.Filter.SearchTerm ?? string.Empty);
            }
        }

        /// <summary>
        /// Handles the delete vehicle event.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle to delete.</param>
        protected async Task HandleDeleteVehicleAsync(Guid vehicleId)
        {
            var vehicle = ViewModel.Vehicles.FirstOrDefault(v => v.Id == vehicleId);
            if (vehicle == null)
            {
                Logger.LogWarning("Vehicle with ID {VehicleId} not found for deletion", vehicleId);
                return;
            }

            var result = await DialogService.Confirm(
                $"Are you sure you want to delete the vehicle \"{vehicle.Name}\"? This action cannot be undone and will also delete all associated devices and configurations.",
                "Confirm Deletion",
                new ConfirmOptions()
                {
                    OkButtonText = "Delete",
                    CancelButtonText = "Cancel",
                    Width = "450px",
                    CssClass = "barret-confirmation-dialog"
                });

            if (result == true)
            {
                var success = await ViewModel.DeleteVehicleAsync(vehicleId);
                if (!success)
                {
                    // Could show a toast notification or other feedback here
                    Logger.LogWarning("Failed to delete vehicle with ID: {VehicleId}", vehicleId);
                }
            }
        }

        /// <summary>
        /// Handles the copy vehicle event.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle to copy.</param>
        protected void HandleCopyVehicleAsync(Guid vehicleId)
        {
            // Find the vehicle in the list to get its name
            var vehicle = ViewModel.Vehicles.FirstOrDefault(v => v.Id == vehicleId);
            if (vehicle != null)
            {
                vehicleToCopyId = vehicleId;
                vehicleToCopyName = vehicle.Name;
                showCopyDialog = true;
            }
            else
            {
                Logger.LogWarning("Vehicle with ID {VehicleId} not found for copying", vehicleId);
            }
        }

        /// <summary>
        /// Confirms the copying of a vehicle with a new name.
        /// </summary>
        /// <param name="newName">The name for the copied vehicle.</param>
        protected async Task ConfirmCopyVehicleAsync(string newName)
        {
            var success = await ViewModel.CopyVehicleAsync(vehicleToCopyId, newName);
            if (!success)
            {
                // Could show a toast notification or other feedback here
                Logger.LogWarning("Failed to copy vehicle with ID: {VehicleId}", vehicleToCopyId);
            }

            // Close the copy dialog
            showCopyDialog = false;
        }

        /// <summary>
        /// Cancels the copying of a vehicle.
        /// </summary>
        protected void CancelCopyVehicle()
        {
            showCopyDialog = false;
        }

        /// <summary>
        /// Clears any error state in the ViewModel.
        /// </summary>
        protected void ClearError()
        {
            ViewModel.ClearError();
        }

        /// <summary>
        /// Handles vehicle row selection in the RadzenDataGrid.
        /// </summary>
        /// <param name="vehicle">The selected vehicle.</param>
        protected void OnVehicleRowSelect(VehicleData vehicle)
        {
            NavigateToVehicleConfiguration(vehicle.Id);
        }
    }
}
