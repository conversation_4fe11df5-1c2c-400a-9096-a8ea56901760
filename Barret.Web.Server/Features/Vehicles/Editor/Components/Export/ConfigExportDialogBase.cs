using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.JSInterop;
using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Services.Areas.Devices.Factories;
using Barret.Services.Core.Areas.Devices.Factories;
using Barret.Services.Core.Areas.Vehicles;
using Barret.Web.Server.Features.Vehicles.Editor.ViewModels;
using Barret.Web.Server.Services;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Export
{
    /// <summary>
    /// Base class for the config export dialog component
    /// </summary>
    public class ConfigExportDialogBase : ComponentBase
    {
        [Inject] protected ILogger<ConfigExportDialogBase> Logger { get; set; } = null!;
        [Inject] protected ILoggerFactory LoggerFactory { get; set; } = null!;
        [Inject] protected IJSRuntime JSRuntime { get; set; } = null!;
        [Inject] protected IVehicleService<Vessel, Barret.Shared.DTOs.Vehicles.Vessels.VesselDto> VesselService { get; set; } = null!;
        [Inject] protected IConfigExportService ConfigExportService { get; set; } = null!;
        [Inject] protected IFileDownloadService FileDownloadService { get; set; } = null!;
        [Inject] protected IDeviceFactory DeviceFactory { get; set; } = null!;
        [Inject] protected IVehicleDataProvider VehicleDataProvider { get; set; } = null!;

        [Parameter] public bool Visible { get; set; }
        [Parameter] public EventCallback<bool> VisibleChanged { get; set; }
        [Parameter] public Guid? VesselId { get; set; }

        protected ConfigExportViewModel ViewModel { get; set; } = null!;

        /// <summary>
        /// Initializes the component
        /// </summary>
        protected override void OnInitialized()
        {
            base.OnInitialized();

            // Create a logger specifically for the ViewModel
            var viewModelLogger = LoggerFactory != null
                ? LoggerFactory.CreateLogger<ConfigExportViewModel>()
                : NullLogger<ConfigExportViewModel>.Instance;

            ViewModel = new ConfigExportViewModel(
                viewModelLogger,
                ConfigExportService,
                FileDownloadService);
        }

        /// <summary>
        /// Handles the export button click
        /// </summary>
        protected async Task HandleExportAsync()
        {
            if (!VesselId.HasValue)
            {
                ViewModel.ErrorMessage = "Vessel ID is required";
                return;
            }

            try
            {
                Logger.LogInformation("CONFIGEXPORTDIALOG: Loading vessel {VesselId} with full data for configuration export", VesselId.Value);

                // Use VehicleDataProvider to load the vessel with all related data (devices, connections, models, etc.)
                // NOTE: This should be replaced with VesselInstanceManager once it's properly registered
                var vesselEntity = await VehicleDataProvider.GetVesselWithAllDataAsync(VesselId.Value);

                // Validate vessel data immediately after loading
                var deviceCount = vesselEntity.GetAllDevices().Count();
                Logger.LogInformation("CONFIGEXPORTDIALOG: Loaded vessel {VesselId} with {DeviceCount} devices for configuration export",
                    vesselEntity.Id, deviceCount);

                // Check for duplicate devices at the entry point
                var deviceList = vesselEntity.GetAllDevices().ToList();
                var deviceIds = deviceList.Select(d => d.Id).ToList();
                var uniqueDeviceIds = deviceIds.Distinct().ToList();

                if (deviceIds.Count != uniqueDeviceIds.Count)
                {
                    var duplicateCount = deviceIds.Count - uniqueDeviceIds.Count;
                    Logger.LogError("CONFIGEXPORTDIALOG: CRITICAL ISSUE - Vessel {VesselId} has {DuplicateCount} duplicate devices at entry point! Total: {TotalCount}, Unique: {UniqueCount}",
                        VesselId.Value, duplicateCount, deviceIds.Count, uniqueDeviceIds.Count);
                }
                else
                {
                    Logger.LogInformation("CONFIGEXPORTDIALOG: VALIDATION PASSED - No duplicate devices detected at entry point");
                }

                // Export configurations using the fully loaded entity
                await ViewModel.ExportConfigurationsAsync(vesselEntity);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error exporting configs for vessel {VesselId}", VesselId.Value);
                ViewModel.ErrorMessage = $"Error exporting configs: {ex.Message}";
            }
        }

        /// <summary>
        /// Handles the cancel button click
        /// </summary>
        protected void HandleCancelExport()
        {
            ViewModel.CancelExport();
        }

        /// <summary>
        /// Handles the close button click
        /// </summary>
        protected async Task HandleCloseAsync()
        {
            await VisibleChanged.InvokeAsync(false);
        }

        // No longer needed - RadzenDialog handles closing differently
    }
}
