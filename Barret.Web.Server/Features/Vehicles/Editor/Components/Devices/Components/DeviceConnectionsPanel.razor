@using Barret.Core.Areas.Devices.Enums
@using Barret.Shared.DTOs.Devices
@using Barret.Services.Core.Areas.Devices.Queries
@using Barret.Services.Core.Areas.Vehicles
@using Barret.Core.Areas.Vehicles.Models.Vessel
@using Barret.Shared.DTOs.Vehicles.Vessels
@using Barret.Web.Server.Services
@using Barret.Web.Server.Services.DTO
@using Barret.Web.Server.Features.Vehicles.Services
@using Microsoft.Extensions.Logging
@using Radzen.Blazor
@using System.Linq

@inject IDeviceQueryService DeviceQueryService
@inject IVehicleService VehicleService
@inject DeviceDtoService DeviceDtoService
@inject IBarretToastNotificationService ToastService
@inject ILogger<DeviceConnectionsPanel> Logger

<div class="relative device-connections-panel">
    <div class="flex justify-between items-center mb-4 px-4 pt-4">
        <h4 class="text-lg font-medium text-gray-900">Device Connections</h4>
        <button class="flex items-center gap-2 h-10 px-4 rounded-full bg-gray-900 text-white hover:bg-gray-800 transition-colors connection-button"
                @onclick="OpenAddConnectionDialog">
            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            <span>Add Connection</span>
        </button>
    </div>

    @if (isLoading)
    {
        <div class="flex justify-center items-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
    }
    else if (connections.Count == 0)
    {
        <div class="text-center py-8 bg-gray-50 rounded-lg">
            <div class="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <svg class="h-8 w-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                    <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
                </svg>
            </div>
            <h3 class="text-xl font-medium text-gray-900 mb-2">No Connections</h3>
            <p class="text-gray-500 mb-6 max-w-md mx-auto">
                This device doesn't have any connections yet. Add a connection to link this device with other devices in the system.
            </p>
            <button class="flex items-center gap-2 h-10 px-4 rounded-full bg-gray-900 text-white hover:bg-gray-800 transition-colors mx-auto connection-button"
                    @onclick="OpenAddConnectionDialog">
                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                <span>Add Connection</span>
            </button>
        </div>
    }
    else
    {
        <div class="w-full border-b border-gray-100 mb-4">
            <div class="flex overflow-x-auto hide-scrollbar">
                <button
                    @onclick="() => activeTabIndex = 0"
                    class="@GetTabClasses(0)"
                    aria-current="@(activeTabIndex == 0 ? "page" : null)">
                    <div class="inline-flex items-center gap-2">
                        <svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M4 6h16M4 12h16m-7 6h7"></path>
                        </svg>
                        Devices Connected To This Device
                    </div>
                </button>

                <button
                    @onclick="() => activeTabIndex = 1"
                    class="@GetTabClasses(1)"
                    aria-current="@(activeTabIndex == 1 ? "page" : null)">
                    <div class="inline-flex items-center gap-2">
                        <svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
                        </svg>
                        Connected To
                    </div>
                </button>
            </div>
        </div>

        @if (activeTabIndex == 0)
        {
            var devicesUsingThisAsInterface = connections
                .Where(c => c.InterfaceDeviceId == Device.Id)
                .ToList();

            if (!devicesUsingThisAsInterface.Any())
            {
                <div class="text-center py-4 text-gray-500">
                    No devices are currently using this device as an interface.
                </div>
            }
            else
            {
                <RadzenDataGrid Data="@devicesUsingThisAsInterface"
                               TItem="DeviceConnectionDto"
                               AllowFiltering="false"
                               AllowSorting="true"
                               AllowPaging="false"
                               class="barret-grid bg-white overflow-hidden">
                    <Columns>
                        <RadzenDataGridColumn TItem="DeviceConnectionDto" Title="Connected Device" Width="40%">
                            <Template Context="connection">
                                @{
                                    var connectedDevice = AllDevices.FirstOrDefault(d => d.Id == connection.ConnectedDeviceId);
                                }
                                <div class="flex items-center">
                                    <span class="text-gray-900 font-medium">@(connectedDevice?.Name ?? "Unknown Device")</span>
                                </div>
                            </Template>
                        </RadzenDataGridColumn>

                        <RadzenDataGridColumn TItem="DeviceConnectionDto" Property="Type" Title="Type" Width="20%" />
                        <RadzenDataGridColumn TItem="DeviceConnectionDto" Property="Direction" Title="Direction" Width="20%" />

                        <RadzenDataGridColumn TItem="DeviceConnectionDto" Title="Actions" Width="20%" Sortable="false">
                            <Template Context="connection">
                                <div class="barret-grid-actions-right space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-gray-100"
                                            @onclick="() => EditConnection(connection)">
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                        </svg>
                                    </button>
                                    <button class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-gray-100"
                                            @onclick="() => DeleteConnection(connection)">
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M3 6h18"></path>
                                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path>
                                            <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                            <line x1="10" y1="11" x2="10" y2="17"></line>
                                            <line x1="14" y1="11" x2="14" y2="17"></line>
                                        </svg>
                                    </button>
                                </div>
                            </Template>
                        </RadzenDataGridColumn>
                    </Columns>

                    <FooterTemplate>
                        <div class="text-sm text-gray-500 p-2">
                            Total: @devicesUsingThisAsInterface.Count connection(s)
                        </div>
                    </FooterTemplate>
                </RadzenDataGrid>
            }
        }

        @if (activeTabIndex == 1)
        {
            var interfacesThisDeviceConnectsTo = connections
                .Where(c => c.ConnectedDeviceId == Device.Id)
                .ToList();

            if (!interfacesThisDeviceConnectsTo.Any())
            {
                <div class="text-center py-4 text-gray-500">
                    This device is not connected to any interface devices.
                </div>
            }
            else
            {
                <RadzenDataGrid Data="@interfacesThisDeviceConnectsTo"
                               TItem="DeviceConnectionDto"
                               AllowFiltering="false"
                               AllowSorting="true"
                               AllowPaging="false"
                               class="barret-grid bg-white overflow-hidden">
                    <Columns>
                        <RadzenDataGridColumn TItem="DeviceConnectionDto" Title="Interface Device" Width="40%">
                            <Template Context="connection">
                                @{
                                    var interfaceDevice = AllDevices.FirstOrDefault(d => d.Id == connection.InterfaceDeviceId);
                                }
                                <div class="flex items-center">
                                    <span class="text-gray-900 font-medium">@(interfaceDevice?.Name ?? "Unknown Device")</span>
                                </div>
                            </Template>
                        </RadzenDataGridColumn>

                        <RadzenDataGridColumn TItem="DeviceConnectionDto" Property="Type" Title="Type" Width="20%" />
                        <RadzenDataGridColumn TItem="DeviceConnectionDto" Property="Direction" Title="Direction" Width="20%" />

                        <RadzenDataGridColumn TItem="DeviceConnectionDto" Title="Actions" Width="20%" Sortable="false">
                            <Template Context="connection">
                                <div class="barret-grid-actions-right space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-gray-100"
                                            @onclick="() => EditConnection(connection)">
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                        </svg>
                                    </button>
                                    <button class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-gray-100"
                                            @onclick="() => DeleteConnection(connection)">
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M3 6h18"></path>
                                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path>
                                            <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                            <line x1="10" y1="11" x2="10" y2="17"></line>
                                            <line x1="14" y1="11" x2="14" y2="17"></line>
                                        </svg>
                                    </button>
                                </div>
                            </Template>
                        </RadzenDataGridColumn>
                    </Columns>

                    <FooterTemplate>
                        <div class="text-sm text-gray-500 p-2">
                            Total: @interfacesThisDeviceConnectsTo.Count connection(s)
                        </div>
                    </FooterTemplate>
                </RadzenDataGrid>
            }
        }
    }
</div>

<!-- Connection Editor Dialog -->
<DxPopup @bind-Visible="@isConnectionEditorVisible"
         HeaderText="@connectionEditorTitle"
         ShowFooter="true"
         Width="600px"
         CloseOnEscape="true"
         CloseOnOutsideClick="false"
         CssClass="rounded-lg">
    <HeaderTemplate>
        <div class="flex items-center">
            <svg class="h-6 w-6 mr-2 text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
            </svg>
            <span class="text-gray-900 font-medium text-lg">@connectionEditorTitle</span>
        </div>
    </HeaderTemplate>
    <Content>
        <div class="p-6">
            <!-- Connection Direction Selector -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Connection Direction</label>
                <div class="grid grid-cols-2 gap-4">
                    <div class="@(connectionMode == ConnectionMode.ConnectTo
                            ? "bg-gray-50 border-gray-900 shadow-md"
                            : "bg-white border-gray-200 hover:border-gray-400")
                            border-2 rounded-lg p-4 cursor-pointer transition-all duration-200"
                         @onclick="() => connectionMode = ConnectionMode.ConnectTo">
                        <div class="flex items-center justify-center mb-2">
                            <svg class="@(connectionMode == ConnectionMode.ConnectTo ? "text-gray-900" : "text-gray-500") h-8 w-8 transition-colors"
                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
                            </svg>
                        </div>
                        <div class="text-center">
                            <div class="@(connectionMode == ConnectionMode.ConnectTo ? "font-semibold text-gray-900" : "font-medium text-gray-700")">
                                Connected To
                            </div>
                            <div class="text-sm text-gray-500">Connect this device to an interface device</div>
                        </div>
                    </div>

                    <div class="@(connectionMode == ConnectionMode.ServeAsInterface
                            ? "bg-gray-50 border-gray-900 shadow-md"
                            : "bg-white border-gray-200 hover:border-gray-400")
                            border-2 rounded-lg p-4 cursor-pointer transition-all duration-200"
                         @onclick="() => connectionMode = ConnectionMode.ServeAsInterface">
                        <div class="flex items-center justify-center mb-2">
                            <svg class="@(connectionMode == ConnectionMode.ServeAsInterface ? "text-gray-900" : "text-gray-500") h-8 w-8 transition-colors"
                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M4 6h16M4 12h16m-7 6h7"></path>
                            </svg>
                        </div>
                        <div class="text-center">
                            <div class="@(connectionMode == ConnectionMode.ServeAsInterface ? "font-semibold text-gray-900" : "font-medium text-gray-700")">
                                Devices Connected To This Device
                            </div>
                            <div class="text-sm text-gray-500">Allow other devices to connect to this device</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Device Selection -->
            <div class="mb-6">
                <div class="flex items-center mb-2">
                    <svg class="h-5 w-5 mr-2 text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
                        <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
                    </svg>
                    <label class="block text-sm font-medium text-gray-700">
                        @(connectionMode == ConnectionMode.ConnectTo ? "Select Interface Device" : "Select Device to Connect")
                    </label>
                </div>
                <div class="relative">
                    <RadzenDropDown @bind-Value="@selectedDeviceId"
                                   Data="@(connectionMode == ConnectionMode.ConnectTo ? availableInterfaceDevices : availableDevicesToConnect)"
                                   TextProperty="Name"
                                   ValueProperty="Id"
                                   Placeholder="@(connectionMode == ConnectionMode.ConnectTo ? "Select an interface device..." : "Select a device to connect...")"
                                   AllowClear="true"
                                   class="barret-input w-full">
                        <Template>
                            <div class="flex items-center py-1">
                                <span class="font-medium">@((context as DeviceDto)?.Name)</span>
                                <span class="ml-2 text-xs text-gray-500">(@((context as DeviceDto)?.DeviceRole))</span>
                            </div>
                        </Template>
                    </RadzenDropDown>
                </div>
                <p class="mt-1 text-xs text-gray-500">
                    @(connectionMode == ConnectionMode.ConnectTo
                        ? "Select the device that this device will connect to."
                        : "Select the device that will connect to this device.")
                </p>
            </div>

            <!-- Connection Type -->
            <div class="mb-6">
                <div class="flex items-center mb-2">
                    <svg class="h-5 w-5 mr-2 text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                    </svg>
                    <label class="block text-sm font-medium text-gray-700">Connection Type</label>
                </div>
                <RadzenDropDown @bind-Value="@selectedConnectionType"
                               Data="@ConnectionTypes"
                               Placeholder="Select a connection type..."
                               AllowClear="true"
                               class="barret-input w-full">
                    <Template>
                        <div class="py-1">
                            <span class="font-medium">@context</span>
                        </div>
                    </Template>
                </RadzenDropDown>
                <p class="mt-1 text-xs text-gray-500">Specify the type of connection between these devices.</p>
            </div>

            <!-- Connection Direction -->
            <div class="mb-2">
                <div class="flex items-center mb-2">
                    <svg class="h-5 w-5 mr-2 text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="8 12 12 16 16 12"></polyline>
                        <line x1="12" y1="8" x2="12" y2="16"></line>
                    </svg>
                    <label class="block text-sm font-medium text-gray-700">Data Flow Direction</label>
                </div>
                <RadzenDropDown @bind-Value="@selectedConnectionDirection"
                               Data="@ConnectionDirections"
                               Placeholder="Select a direction..."
                               AllowClear="true"
                               class="barret-input w-full">
                    <Template>
                        <div class="py-1">
                            <span class="font-medium">@context</span>
                            <span class="ml-2 text-xs text-gray-500">
                                @switch (context)
                                {
                                    case ConnectionDirection.Inbound:
                                        <text>(Device to System)</text>
                                        break;
                                    case ConnectionDirection.Outbound:
                                        <text>(System to Device)</text>
                                        break;
                                    case ConnectionDirection.Duplex:
                                        <text>(Bidirectional)</text>
                                        break;
                                }
                            </span>
                        </div>
                    </Template>
                </RadzenDropDown>
                <p class="mt-1 text-xs text-gray-500">Specify the direction of data flow between these devices.</p>
            </div>
        </div>
    </Content>
    <FooterTemplate>
        <div class="flex justify-end gap-3 p-4 bg-gray-50 rounded-b-lg">
            <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                    @onclick="() => isConnectionEditorVisible = false">
                Cancel
            </button>
            <button class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none transition-colors"
                    @onclick="SaveConnection"
                    disabled="@(selectedDeviceId == Guid.Empty || selectedDeviceId == Device.Id)">
                <div class="flex items-center">
                    <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                        <polyline points="17 21 17 13 7 13 7 21"></polyline>
                        <polyline points="7 3 7 8 15 8"></polyline>
                    </svg>
                    Save Connection
                </div>
            </button>
        </div>
    </FooterTemplate>
</DxPopup>

<!-- Delete Confirmation Dialog -->
<DxPopup @bind-Visible="@isDeleteConfirmationVisible"
         HeaderText="Confirm Deletion"
         ShowFooter="true"
         Width="450px"
         CloseOnEscape="true"
         CloseOnOutsideClick="false"
         CssClass="rounded-lg">
    <HeaderTemplate>
        <div class="flex items-center">
            <svg class="h-6 w-6 mr-2 text-red-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                <line x1="12" y1="9" x2="12" y2="13"></line>
                <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
            <span class="text-gray-900 font-medium text-lg">Confirm Deletion</span>
        </div>
    </HeaderTemplate>
    <Content>
        <div class="p-6">
            <div class="flex items-start mb-4">
                <svg class="h-12 w-12 text-red-100 bg-red-600 p-2 rounded-full mr-4 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 6h18"></path>
                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path>
                    <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                </svg>
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Delete Connection</h3>
                    <p class="text-gray-600">
                        Are you sure you want to delete this connection? This action cannot be undone and may affect system functionality.
                    </p>
                    @if (connectionToDelete != null)
                    {
                        var interfaceDevice = AllDevices.FirstOrDefault(d => d.Id == connectionToDelete.InterfaceDeviceId);
                        <div class="mt-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="text-sm font-medium">Connection Details:</span>
                                    <div class="mt-1 text-sm text-gray-600">
                                        <div>Interface Device: <span class="font-medium">@(interfaceDevice?.Name ?? "Unknown Device")</span></div>
                                        <div>Type: <span class="font-medium">@connectionToDelete.Type</span></div>
                                        <div>Direction: <span class="font-medium">@connectionToDelete.Direction</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </Content>
    <FooterTemplate>
        <div class="flex justify-end gap-3 p-4 bg-gray-50 rounded-b-lg">
            <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                    @onclick="() => isDeleteConfirmationVisible = false">
                Cancel
            </button>
            <button class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none transition-colors"
                    @onclick="ConfirmDeleteConnection">
                <div class="flex items-center">
                    <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 6h18"></path>
                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path>
                        <line x1="10" y1="11" x2="10" y2="17"></line>
                        <line x1="14" y1="11" x2="14" y2="17"></line>
                    </svg>
                    Delete Connection
                </div>
            </button>
        </div>
    </FooterTemplate>
</DxPopup>

<style>
    .device-connections-panel {
        position: relative;
        overflow: visible;
        background-color: white;
    }

    .connection-button {
        position: relative;
        z-index: 999 !important;
    }

    /* Remove borders from tab content */
    ::deep .dxbl-tab-content {
        border: none !important;
        padding: 0 !important;
    }

    /* Remove borders from tabs */

    /* Style the connection tabs to match vehicle list tabs */
    .connection-tabs {
        margin-top: 0 !important;
        background-color: transparent !important;
    }

    ::deep .dxbl-tabs,
    ::deep .dxbl-tabs-scrollable,
    ::deep .dxbl-tabs-top {
        background-color: transparent !important;
        background-image: none !important;
        border: none !important;
        box-shadow: none !important;
    }

    ::deep .dxbl-tabs-header {
        background-color: transparent !important;
        background-image: none !important;
        border: none !important;
        padding: 0 !important;
        box-shadow: none !important;
    }

    ::deep .dxbl-tabs-header-item {
        background-color: transparent !important;
        background-image: none !important;
        border: none !important;
        border-bottom: 1px solid transparent !important;
        border-radius: 0 !important;
        box-shadow: none !important;
        padding: 0.75rem 1.25rem !important;
        margin-right: 1rem !important;
        transition: all 0.3s ease-out !important;
        color: #4b5563 !important; /* text-gray-600 */
        font-weight: 500 !important;
    }

    ::deep .dxbl-tabs-header-item.active {
        background-color: transparent !important;
        background-image: none !important;
        border: none !important;
        border-bottom: 2px solid #111827 !important; /* gray-900 */
        box-shadow: none !important;
        color: #111827 !important; /* text-gray-900 */
        font-weight: 600 !important;
    }

    ::deep .dxbl-tabs-header-item:hover:not(.active) {
        background-color: transparent !important;
        background-image: none !important;
        border: none !important;
        border-bottom: 1px solid #d1d5db !important; /* gray-300 */
        box-shadow: none !important;
        color: #111827 !important; /* text-gray-900 */
    }
</style>

@code {
    [Parameter]
    public DeviceDto Device { get; set; } = null!;

    [Parameter]
    public EventCallback<DeviceDto> OnDeviceChanged { get; set; }

    [Parameter]
    public List<DeviceDto> AllDevices { get; set; } = new();

    [Parameter]
    public VesselDto Vessel { get; set; } = null!;

    private bool isLoading = true;
    private List<DeviceConnectionDto> connections = new();
    private List<DeviceDto> availableInterfaceDevices = new();
    private List<DeviceDto> availableDevicesToConnect = new();
    private int activeTabIndex = 0;

    /// <summary>
    /// Gets the CSS classes for a tab based on whether it's active.
    /// </summary>
    /// <param name="index">The tab index.</param>
    /// <returns>The CSS classes for the tab.</returns>
    protected string GetTabClasses(int index) => activeTabIndex == index
        ? "inline-flex items-center gap-2 px-6 py-3 border-b-2 border-gray-900 text-gray-900 font-medium relative focus:outline-none"
        : "inline-flex items-center gap-2 px-6 py-3 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium transition-colors duration-200 focus:outline-none";

    // Connection mode enum
    private enum ConnectionMode
    {
        ConnectTo,
        ServeAsInterface
    }

    // Connection editor properties
    private bool isConnectionEditorVisible = false;
    private string connectionEditorTitle = "Add Connection";
    private ConnectionMode connectionMode = ConnectionMode.ConnectTo;
    private Guid selectedDeviceId = Guid.Empty;
    private ConnectionType selectedConnectionType = ConnectionType.Standard;
    private ConnectionDirection selectedConnectionDirection = ConnectionDirection.Duplex;
    private bool isEditingExistingConnection = false;
    private DeviceConnectionDto? connectionBeingEdited;

    // Delete confirmation properties
    private bool isDeleteConfirmationVisible = false;
    private DeviceConnectionDto? connectionToDelete;

    // Pre-populated lists for enums
    private ConnectionType[] ConnectionTypes => Enum.GetValues<ConnectionType>();
    private ConnectionDirection[] ConnectionDirections => Enum.GetValues<ConnectionDirection>();

    protected override async Task OnInitializedAsync()
    {
        await LoadDataAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            if (Device == null || Vessel == null)
            {
                connections = new List<DeviceConnectionDto>();
                return;
            }

            // Initialize connections collection if needed
            Device.Connections ??= [];
            Vessel.DeviceConnections ??= [];

            // Create a new list to hold all connections relevant to this device
            var allConnections = new List<DeviceConnectionDto>();

            // Add outgoing connections from the device's Connections collection
            // These are connections where this device is the connectedDevice
            allConnections.AddRange(Device.Connections);

            // Add incoming connections from the vessel's DeviceConnections collection
            // These are connections where this device is the interfaceDevice
            var incomingConnections = Vessel.DeviceConnections
                .Where(c => c.InterfaceDeviceId == Device.Id)
                .ToList();

            // Log the number of connections found
            Logger.LogDebug("Found {OutgoingCount} outgoing and {IncomingCount} incoming connections for device {DeviceId}",
                Device.Connections.Count, incomingConnections.Count, Device.Id);

            // Add incoming connections that aren't already in the list
            foreach (var connection in incomingConnections)
            {
                // Check if this connection is already in the list
                if (!allConnections.Any(c =>
                    c.ConnectedDeviceId == connection.ConnectedDeviceId &&
                    c.InterfaceDeviceId == connection.InterfaceDeviceId))
                {
                    allConnections.Add(connection);
                }
            }

            // Set the connections list
            connections = allConnections;

            // Filter available devices using the vehicle service
            availableInterfaceDevices = VehicleService
                .GetCompatibleInterfaceDevices(Device, AllDevices)
                .ToList();

            // Filter devices that can connect to this device as an interface
            availableDevicesToConnect = VehicleService
                .GetDevicesThatCanConnectToInterface(Device, AllDevices)
                .ToList();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading device connections for device {DeviceId}", Device?.Id);
            ToastService.ShowToast("Error", $"Failed to load connections: {ex.Message}", ToastType.Error);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void OpenAddConnectionDialog()
    {
        // Reset the form
        connectionEditorTitle = "Add Connection";
        selectedDeviceId = Guid.Empty;
        selectedConnectionType = ConnectionType.Standard;
        selectedConnectionDirection = ConnectionDirection.Duplex;
        connectionMode = ConnectionMode.ConnectTo;
        isEditingExistingConnection = false;
        connectionBeingEdited = null;

        // Show the dialog
        isConnectionEditorVisible = true;
    }

    private void EditConnection(DeviceConnectionDto connection)
    {
        // Set the form values
        connectionEditorTitle = "Edit Connection";

        // Determine the connection mode based on the connection being edited
        if (connection.InterfaceDeviceId == Device.Id)
        {
            // This device is the interface, so we're in ServeAsInterface mode
            connectionMode = ConnectionMode.ServeAsInterface;
            selectedDeviceId = connection.ConnectedDeviceId;
        }
        else
        {
            // This device is connecting to another interface, so we're in ConnectTo mode
            connectionMode = ConnectionMode.ConnectTo;
            selectedDeviceId = connection.InterfaceDeviceId;
        }

        selectedConnectionType = connection.Type;
        selectedConnectionDirection = connection.Direction;
        isEditingExistingConnection = true;
        connectionBeingEdited = connection;

        // Show the dialog
        isConnectionEditorVisible = true;
    }

    private void DeleteConnection(DeviceConnectionDto connection)
    {
        connectionToDelete = connection;
        isDeleteConfirmationVisible = true;
    }

    private async Task SaveConnection()
    {
        try
        {
            if (Device == null)
            {
                ToastService.ShowToast("Error", "Device information is missing", ToastType.Error);
                return;
            }

            if (selectedDeviceId == Guid.Empty)
            {
                ToastService.ShowToast("Error", "Please select a device", ToastType.Error);
                return;
            }

            // Validate the connection using the VehicleService
            var selectedDevice = AllDevices.FirstOrDefault(d => d.Id == selectedDeviceId);
            if (selectedDevice == null)
            {
                ToastService.ShowToast("Error", "Selected device not found", ToastType.Error);
                return;
            }

            // Check compatibility based on the connection mode
            bool isCompatible = connectionMode == ConnectionMode.ConnectTo
                ? VehicleService.CanDevicesConnect(Device, selectedDevice)
                : VehicleService.CanDevicesConnect(selectedDevice, Device);

            if (!isCompatible)
            {
                ToastService.ShowToast("Error", "These devices are not compatible", ToastType.Error);
                return;
            }

            bool success = false;

            if (isEditingExistingConnection && connectionBeingEdited != null)
            {
                // Update existing connection
                if (connectionMode == ConnectionMode.ConnectTo)
                {
                    // This device connects to the selected interface device
                    success = DeviceDtoService.UpdateConnection(
                        Device,
                        selectedDeviceId,
                        selectedConnectionType,
                        selectedConnectionDirection);

                    if (success)
                    {
                        ToastService.ShowToast("Success", "Connection updated successfully", ToastType.Success);
                        // No need to reload data from database, just update the local list
                        var connection = Device.Connections.FirstOrDefault(c =>
                            c.ConnectedDeviceId == Device.Id &&
                            c.InterfaceDeviceId == selectedDeviceId);

                        if (connection != null)
                        {
                            connection.Type = selectedConnectionType;
                            connection.Direction = selectedConnectionDirection;
                        }

                        await OnDeviceChanged.InvokeAsync(Device);
                    }
                    else
                    {
                        ToastService.ShowToast("Error", "Failed to update connection", ToastType.Error);
                    }
                }
                else // ServeAsInterface
                {
                    // The selected device connects to this device as an interface
                    var otherDevice = AllDevices.FirstOrDefault(d => d.Id == selectedDeviceId);
                    if (otherDevice == null)
                    {
                        ToastService.ShowToast("Error", "Selected device not found", ToastType.Error);
                        return;
                    }

                    success = DeviceDtoService.UpdateConnection(
                        otherDevice,
                        Device.Id,
                        selectedConnectionType,
                        selectedConnectionDirection);

                    if (success)
                    {
                        ToastService.ShowToast("Success", "Connection updated successfully", ToastType.Success);

                        // Update the connection in the other device
                        var connection = otherDevice.Connections.FirstOrDefault(c =>
                            c.ConnectedDeviceId == otherDevice.Id &&
                            c.InterfaceDeviceId == Device.Id);

                        if (connection != null)
                        {
                            connection.Type = selectedConnectionType;
                            connection.Direction = selectedConnectionDirection;
                        }

                        // Refresh the connections list
                        await LoadDataAsync();

                        // Notify that the current device has changed (even though the connection is on the other device)
                        // This ensures the form is marked as dirty
                        await OnDeviceChanged.InvokeAsync(Device);
                    }
                    else
                    {
                        ToastService.ShowToast("Error", "Failed to update connection", ToastType.Error);
                    }
                }
            }
            else
            {
                // Create new connection based on the selected mode
                if (connectionMode == ConnectionMode.ConnectTo)
                {
                    // This device connects to the selected interface device
                    var (connection, errorMessage) = DeviceDtoService.AddConnection(
                        Device,
                        selectedDeviceId,
                        selectedConnectionType,
                        selectedConnectionDirection);

                    if (connection != null)
                    {
                        // Also add the connection to the vessel's DeviceConnections list
                        if (Vessel != null && !Vessel.DeviceConnections.Any(c =>
                            c.ConnectedDeviceId == connection.ConnectedDeviceId &&
                            c.InterfaceDeviceId == connection.InterfaceDeviceId))
                        {
                            Vessel.DeviceConnections.Add(connection);
                            Logger.LogInformation("Added connection to vessel's DeviceConnections list");
                        }

                        success = true;
                        ToastService.ShowToast("Success", "Connection added successfully", ToastType.Success);

                        // Update the local connections list
                        connections = Device.Connections.ToList();

                        await OnDeviceChanged.InvokeAsync(Device);
                    }
                    else
                    {
                        // Show the specific error message
                        ToastService.ShowToast("Error", errorMessage ?? "Failed to add connection", ToastType.Error);
                    }
                }
                else // ServeAsInterface
                {
                    // The selected device connects to this device as an interface
                    var otherDevice = AllDevices.FirstOrDefault(d => d.Id == selectedDeviceId);
                    if (otherDevice == null)
                    {
                        ToastService.ShowToast("Error", "Selected device not found", ToastType.Error);
                        return;
                    }

                    var (connection, errorMessage) = DeviceDtoService.AddConnection(
                        otherDevice,
                        Device.Id,
                        selectedConnectionType,
                        selectedConnectionDirection);

                    if (connection != null)
                    {
                        // Also add the connection to the vessel's DeviceConnections list
                        if (Vessel != null && !Vessel.DeviceConnections.Any(c =>
                            c.ConnectedDeviceId == connection.ConnectedDeviceId &&
                            c.InterfaceDeviceId == connection.InterfaceDeviceId))
                        {
                            Vessel.DeviceConnections.Add(connection);
                            Logger.LogInformation("Added connection to vessel's DeviceConnections list");
                        }

                        success = true;
                        ToastService.ShowToast("Success", "Connection added successfully", ToastType.Success);

                        // Refresh the connections list
                        await LoadDataAsync();

                        // Notify that the current device has changed (even though the connection is on the other device)
                        // This ensures the form is marked as dirty
                        await OnDeviceChanged.InvokeAsync(Device);
                    }
                    else
                    {
                        // Show the specific error message
                        ToastService.ShowToast("Error", errorMessage ?? "Failed to add connection", ToastType.Error);
                    }
                }
            }

            // Close the dialog if successful
            if (success)
            {
                isConnectionEditorVisible = false;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error saving connection for device {DeviceId}", Device?.Id);
            ToastService.ShowToast("Error", $"Failed to save connection: {ex.Message}", ToastType.Error);
        }
    }

    private async Task ConfirmDeleteConnection()
    {
        try
        {
            if (Device == null || connectionToDelete == null)
            {
                ToastService.ShowToast("Error", "Device or connection information is missing", ToastType.Error);
                return;
            }

            bool success = false;

            // Determine which device is the connected device and which is the interface
            if (connectionToDelete.InterfaceDeviceId == Device.Id)
            {
                // This device is the interface, so we need to remove the connection from the connected device
                var connectedDevice = AllDevices.FirstOrDefault(d => d.Id == connectionToDelete.ConnectedDeviceId);
                if (connectedDevice != null)
                {
                    var (removed, removeErrorMessage) = DeviceDtoService.RemoveConnection(
                        connectedDevice,
                        Device.Id);
                    success = removed;
                    if (!removed && removeErrorMessage != null)
                    {
                        ToastService.ShowToast("Error", removeErrorMessage, ToastType.Error);
                        return;
                    }
                }
                else
                {
                    ToastService.ShowToast("Error", "Connected device not found", ToastType.Error);
                    return;
                }
            }
            else
            {
                // This device is the connected device, so we remove the connection directly
                var (removed, removeErrorMessage) = DeviceDtoService.RemoveConnection(
                    Device,
                    connectionToDelete.InterfaceDeviceId);
                success = removed;
                if (!removed && removeErrorMessage != null)
                {
                    ToastService.ShowToast("Error", removeErrorMessage, ToastType.Error);
                    return;
                }
            }

            if (success)
            {
                // Also remove the connection from the vessel's DeviceConnections list
                if (Vessel != null)
                {
                    var vesselConnection = Vessel.DeviceConnections.FirstOrDefault(c =>
                        c.ConnectedDeviceId == connectionToDelete.ConnectedDeviceId &&
                        c.InterfaceDeviceId == connectionToDelete.InterfaceDeviceId);

                    if (vesselConnection != null)
                    {
                        Vessel.DeviceConnections.Remove(vesselConnection);
                        Logger.LogInformation("Removed connection from vessel's DeviceConnections list");
                    }
                    else
                    {
                        // If the connection wasn't found directly, it might be because the IDs are reversed
                        // This can happen when we're deleting a connection where this device is the interface
                        vesselConnection = Vessel.DeviceConnections.FirstOrDefault(c =>
                            c.ConnectedDeviceId == connectionToDelete.InterfaceDeviceId &&
                            c.InterfaceDeviceId == connectionToDelete.ConnectedDeviceId);

                        if (vesselConnection != null)
                        {
                            Vessel.DeviceConnections.Remove(vesselConnection);
                            Logger.LogInformation("Removed reversed connection from vessel's DeviceConnections list");
                        }
                    }
                }

                ToastService.ShowToast("Success", "Connection deleted successfully", ToastType.Success);

                // Refresh the connections list
                await LoadDataAsync();

                // Notify that the device has changed
                await OnDeviceChanged.InvokeAsync(Device);

                // Close the dialog
                isDeleteConfirmationVisible = false;
            }
            else
            {
                ToastService.ShowToast("Error", "Failed to delete connection", ToastType.Error);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error deleting connection for device {DeviceId}", Device?.Id);
            ToastService.ShowToast("Error", $"Failed to delete connection: {ex.Message}", ToastType.Error);
        }
    }


}
