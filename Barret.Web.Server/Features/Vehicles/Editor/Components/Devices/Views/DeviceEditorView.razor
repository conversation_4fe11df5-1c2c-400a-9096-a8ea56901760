@using Barret.Core.Areas.Devices.Enums
@using Barret.Shared.DTOs.Devices
@using Barret.Web.Server.Shared.Components.DeviceEditors.Tabs
@using Barret.Web.Server.Shared.Components.DeviceManagers
@using Barret.Web.Server.Features.Shared.Components.BarretDevExpress
@using Radzen.Blazor
@inherits DeviceEditorViewBase

<RadzenDialog @bind-Visible="@ViewModel.IsVisible"
              Title="@($"{(ViewModel.IsAdding ? "Add" : "Edit")} {ViewModel.Device.DeviceRole}")"
              Width="900px"
              Height="auto"
              Resizable="true"
              Draggable="true"
              CloseDialogOnEsc="true"
              CloseDialogOnOverlayClick="false"
              class="barret-complex-dialog device-editor-dialog">
    <div class="barret-dialog-content">
        <div class="barret-dialog-header">
            <div class="flex items-center">
                <i class="bi bi-device-ssd text-blue-600 mr-2 text-xl"></i>
                <span class="text-lg font-semibold text-gray-900">@($"{(ViewModel.IsAdding ? "Add" : "Edit")} {ViewModel.Device.DeviceRole}")</span>
            </div>
        </div>

        <div class="barret-dialog-body">
            <div class="device-editor-container">
                <RadzenTabs @bind-SelectedIndex="@ViewModel.ActiveTabIndex"
                           class="device-editor-tabs">
                    @foreach (var tab in TabService.GetTabsForDevice(ViewModel.Device))
                    {
                        <RadzenTabsItem Text="@tab.Name" Icon="@GetIconClassForTab(tab.Name)">
                            <div class="tab-content-panel p-4">
                                @if (tab.Name == "General")
                                {
                                    <BasicInfoTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                }
                                else if (tab.Name == "Make/Model")
                                {
                                    <ModelTab
                                        Device="@ViewModel.Device"
                                        Manufacturers="@ViewModel.Manufacturers"
                                        IsModelRequired="false"
                                        ShowValidationErrors="@(!ViewModel.IsDeviceValid)"
                                        OnManufacturerSelected="@OnManufacturerChangedAsync"
                                        OnPropertyChanged="@OnTabPropertyChanged" />
                                }
                                else if (tab.Name == "Position")
                                {
                                    @if (tab.ComponentType == typeof(MaritimePositionTab))
                                    {
                                        <MaritimePositionTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                    else
                                    {
                                        <PositionTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                }
                                @* else if (tab.Name == "Connection")
                                {
                                    <ConnectionTab Device="@ViewModel.Device" RequireConnection="false" />
                                } *@
                                else if (tab.Name == "Settings")
                                {
                                    @if (tab.ComponentType == typeof(CameraTab))
                                    {
                                        <CameraTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                    else if (tab.ComponentType == typeof(EngineTab))
                                    {
                                        <EngineTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                    else if (tab.ComponentType == typeof(ThrusterTab))
                                    {
                                        <ThrusterTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                    else if (tab.ComponentType == typeof(RadarTab))
                                    {
                                        <RadarTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                    else if (tab.ComponentType == typeof(LightTab))
                                    {
                                        <LightTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                    else if (tab.ComponentType == typeof(RudderTab))
                                    {
                                        <RudderTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                    else if (tab.ComponentType == typeof(HornTab))
                                    {
                                        <HornTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                    else if (tab.ComponentType == typeof(AntennaTab))
                                    {
                                        <AntennaTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                    else if (tab.ComponentType == typeof(AutopilotTab))
                                    {
                                        <AutopilotTab Device="@ViewModel.Device" OnPropertyChanged="@OnTabPropertyChanged" />
                                    }
                                }
                                else if (tab.Name == "Alarms")
                                {
                                    <AlarmManagerTab Device="@ViewModel.Device" OnDeviceChanged="@OnDeviceChanged" />
                                }
                            </div>
                        </RadzenTabsItem>
                    }
                </RadzenTabs>
            </div>
        </div>

        <div class="barret-dialog-footer">
            <div class="flex justify-between items-center p-4">
                <div>
                    @if (!ViewModel.IsDeviceValid)
                    {
                        <div class="barret-alert barret-alert-error">
                            <div class="flex items-center">
                                <i class="bi bi-exclamation-triangle text-red-600 mr-2"></i>
                                <span class="text-red-800 text-sm">Please fill in all required fields</span>
                            </div>
                        </div>
                    }
                </div>
                <div class="barret-dialog-btn-group-right">
                    <RadzenButton Text="Cancel"
                                 Icon="cancel"
                                 ButtonStyle="ButtonStyle.Secondary"
                                 Click="@CancelEdit"
                                 class="barret-btn barret-form-btn" />
                    <RadzenButton Text="@(ViewModel.IsSaving ? "Saving..." : "Save")"
                                 Icon="@(ViewModel.IsSaving ? "" : "save")"
                                 ButtonStyle="ButtonStyle.Primary"
                                 Click="@SaveDeviceAsync"
                                 Disabled="@(!ViewModel.IsDeviceValid || ViewModel.IsSaving)"
                                 IsBusy="@ViewModel.IsSaving"
                                 class="barret-btn barret-form-btn" />
                </div>
            </div>
        </div>
    </div>
</RadzenDialog>
