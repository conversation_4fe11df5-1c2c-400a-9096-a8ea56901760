using Barret.Services.Core.Areas.Vehicles;
using Barret.Services.Core.Areas.Vehicles.Queries;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Vehicles.Vessels;
using Barret.Web.Server.Extensions;
using Barret.Web.Server.Features.Shared;
using Barret.Web.Server.Features.Shared.Components;
using Barret.Web.Server.Features.Shared.Components.BarretDevExpress;
using Barret.Web.Server.Features.Vehicles.Editor.Components.Import.ViewModels;
using Radzen.Blazor;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Rendering;
using Microsoft.Extensions.Logging;
using ReactiveUI.Blazor;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Import.Views
{
    /// <summary>
    /// Base class for the device import dialog view.
    /// </summary>
    public class DeviceImportDialogViewBase : ComponentBase
    {
        /// <summary>
        /// Gets or sets the vessel query service.
        /// </summary>
        [Inject]
        protected IVesselQueryService VesselQueryService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the vessel service.
        /// </summary>
        [Inject]
        protected IVehicleService<Core.Areas.Vehicles.Models.Vessel.Vessel, VesselDto> VesselService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the logger.
        /// </summary>
        [Inject]
        protected ILogger<DeviceImportDialogViewBase> Logger { get; set; } = null!;

        /// <summary>
        /// Gets or sets the view model logger.
        /// </summary>
        [Inject]
        protected ILogger<DeviceImportDialogViewModel> ViewModelLogger { get; set; } = null!;

        /// <summary>
        /// Gets or sets the current vehicle ID.
        /// </summary>
        [Parameter]
        public Guid? VehicleId { get; set; }

        /// <summary>
        /// Gets or sets whether the dialog is visible.
        /// </summary>
        [Parameter]
        public bool Visible { get; set; }

        /// <summary>
        /// Event callback for when the visibility changes.
        /// </summary>
        [Parameter]
        public EventCallback<bool> VisibleChanged { get; set; }

        /// <summary>
        /// Event callback for when devices are imported.
        /// </summary>
        [Parameter]
        public EventCallback<List<DeviceDto>> OnDevicesImported { get; set; }

        /// <summary>
        /// Gets or sets the view model.
        /// </summary>
        protected DeviceImportDialogViewModel ViewModel { get; set; } = null!;

        /// <summary>
        /// Gets or sets the vessel grid reference.
        /// </summary>
        protected RadzenDataGrid<VesselWithDevicesViewModel> VesselGrid { get; set; } = null!;

        /// <summary>
        /// Gets or sets the device grid reference.
        /// </summary>
        protected RadzenDataGrid<DeviceDto> DeviceGrid { get; set; } = null!;

        /// <summary>
        /// Initializes the component.
        /// </summary>
        protected override void OnInitialized()
        {
            // Create the view model
            ViewModel = new DeviceImportDialogViewModel(
                VesselQueryService,
                VesselService,
                ViewModelLogger);
        }

        /// <summary>
        /// Method called when parameters are set.
        /// </summary>
        protected override async Task OnParametersSetAsync()
        {
            // Update the view model's visibility
            if (ViewModel.IsVisible != Visible)
            {
                ViewModel.IsVisible = Visible;

                // Initialize the dialog when it becomes visible
                if (Visible && VehicleId.HasValue)
                {
                    await ViewModel.InitializeAsync(VehicleId.Value);
                }
            }
        }



        /// <summary>
        /// Handles the vessel row selection event.
        /// </summary>
        /// <param name="vesselViewModel">The selected vessel view model.</param>
        protected async Task OnVesselRowSelect(VesselWithDevicesViewModel vesselViewModel)
        {
            await ViewModel.ToggleVesselExpandedAsync(vesselViewModel);
            StateHasChanged();
        }

        /// <summary>
        /// Handles the device row selection event.
        /// </summary>
        /// <param name="device">The selected device.</param>
        protected void OnDeviceRowSelect(DeviceDto device)
        {
            // Toggle the selection state of the device
            ToggleDeviceSelection(device.Id);
            StateHasChanged();
        }



        /// <summary>
        /// Handles the import button click.
        /// </summary>
        protected async Task ImportDevices()
        {
            var importedDevices = await ViewModel.ImportDevicesCommand.Execute().ToTask();
            if (importedDevices != null && importedDevices.Count > 0)
            {
                await OnDevicesImported.InvokeAsync(importedDevices);
            }

            await CloseDialog();
        }

        /// <summary>
        /// Closes the dialog.
        /// </summary>
        protected async Task CloseDialog()
        {
            Visible = false;
            ViewModel.IsVisible = false;
            await VisibleChanged.InvokeAsync(false);
        }

        // No longer needed - RadzenDialog handles closing differently

        /// <summary>
        /// Toggles the selection state of a device.
        /// </summary>
        protected void ToggleDeviceSelection(Guid deviceId)
        {
            ViewModel.ToggleDeviceSelection(deviceId);
        }

        /// <summary>
        /// Gets whether a device is selected.
        /// </summary>
        protected bool IsDeviceSelected(Guid deviceId)
        {
            return ViewModel.SelectedDevices.TryGetValue(deviceId, out bool selected) && selected;
        }



        /// <summary>
        /// Checks if all devices in a vessel are selected.
        /// </summary>
        /// <param name="vesselViewModel">The vessel view model.</param>
        /// <returns>True if all devices in the vessel are selected; otherwise, false.</returns>
        protected bool AreAllDevicesSelectedInVessel(VesselWithDevicesViewModel vesselViewModel)
        {
            if (vesselViewModel == null || !vesselViewModel.IsLoaded || vesselViewModel.Devices.Count == 0)
            {
                return false;
            }

            return vesselViewModel.Devices.All(d =>
                ViewModel.SelectedDevices.TryGetValue(d.Id, out bool selected) && selected);
        }

        /// <summary>
        /// Toggles the selection state of all devices in a vessel.
        /// </summary>
        /// <param name="vesselViewModel">The vessel view model.</param>
        /// <param name="selected">The selection state to set.</param>
        protected void ToggleAllDevicesInVessel(VesselWithDevicesViewModel vesselViewModel, bool selected)
        {
            if (vesselViewModel == null || !vesselViewModel.IsLoaded)
            {
                return;
            }

            foreach (var device in vesselViewModel.Devices)
            {
                ViewModel.SelectedDevices[device.Id] = selected;
            }

            // Update UI
            StateHasChanged();
        }
    }
}
