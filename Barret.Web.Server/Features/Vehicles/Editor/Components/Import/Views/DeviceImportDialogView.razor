@using Barret.Shared.DTOs.Devices
@using Barret.Shared.DTOs.Vehicles.Vessels
@using Barret.Web.Server.Features.Shared.Components.BarretDevExpress
@using Radzen.Blazor
@using Barret.Web.Server.Features.Vehicles.Editor.Components.Import.ViewModels
@inherits DeviceImportDialogViewBase

<RadzenDialog @bind-Visible="@Visible"
              Title="Import Devices"
              Width="900px"
              Height="auto"
              Resizable="true"
              Draggable="true"
              CloseDialogOnEsc="true"
              CloseDialogOnOverlayClick="false"
              class="barret-complex-dialog">
    <div class="barret-dialog-content">
        <div class="barret-dialog-header">
            <div class="flex items-center">
                <i class="bi bi-download text-blue-600 mr-2 text-xl"></i>
                <span class="text-lg font-semibold text-gray-900">Import Devices</span>
            </div>
        </div>

        <div class="barret-dialog-body">
            @if (ViewModel.IsLoading)
            {
                <div class="flex justify-center my-4">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
            }
            else
            {
                <div class="mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Available Vessels and Devices</h3>
                    <p class="text-gray-600">Expand a vessel to view its devices and select the ones you want to import.</p>
                </div>

                <RadzenDataGrid @ref="VesselGrid"
                               Data="@ViewModel.Vessels"
                               TItem="VesselWithDevicesViewModel"
                               Class="barret-grid device-import-grid mb-4"
                               AllowFiltering="true"
                               AllowPaging="true"
                               PageSize="10"
                               RowSelect="@OnVesselRowSelect"
                               ExpandMode="DataGridExpandMode.Single">
                    <Columns>
                        <RadzenDataGridColumn TItem="VesselWithDevicesViewModel" Title="Name" Sortable="true" Filterable="true">
                            <Template Context="vessel">
                                @{
                                    var vesselViewModel = vessel as VesselWithDevicesViewModel;
                                    if (vesselViewModel != null)
                                    {
                                        <div class="flex items-center">
                                            <i class="bi bi-ship mr-2 text-blue-600"></i>
                                            <span class="font-bold">@vesselViewModel.Vessel.Name</span>
                                            <span class="text-gray-500 ml-2">(@vesselViewModel.Vessel.VehicleId)</span>
                                        </div>
                                    }
                                }
                            </Template>
                        </RadzenDataGridColumn>
                        <RadzenDataGridColumn TItem="VesselWithDevicesViewModel" Title="Devices" Width="120px" Sortable="false" Filterable="false">
                            <Template Context="vessel">
                                @{
                                    var vesselViewModel = vessel as VesselWithDevicesViewModel;
                                    if (vesselViewModel != null)
                                    {
                                        <div class="flex items-center">
                                            @if (vesselViewModel.IsLoaded)
                                            {
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">@vesselViewModel.Devices.Count</span>
                                            }
                                            else
                                            {
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">-</span>
                                            }
                                        </div>
                                    }
                                }
                            </Template>
                        </RadzenDataGridColumn>
                        <RadzenDataGridColumn TItem="VesselWithDevicesViewModel" Title="Select All" Width="120px" Sortable="false" Filterable="false">
                            <Template Context="vessel">
                                @{
                                    var vesselViewModel = vessel as VesselWithDevicesViewModel;
                                    if (vesselViewModel != null && vesselViewModel.IsLoaded && vesselViewModel.Devices.Count > 0)
                                    {
                                        <BarretCheckBox Checked="@AreAllDevicesSelectedInVessel(vesselViewModel)"
                                                      CheckedChanged="@((value) => { ToggleAllDevicesInVessel(vesselViewModel, value); StateHasChanged(); })" />
                                    }
                                }
                            </Template>
                        </RadzenDataGridColumn>
                    </Columns>
                    <Template Context="vessel">
                        @{
                            var vesselViewModel = vessel as VesselWithDevicesViewModel;
                            if (vesselViewModel != null)
                            {
                                if (!vesselViewModel.IsLoaded)
                                {
                                    <div class="flex justify-center my-4">
                                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                                    </div>
                                }
                                else if (vesselViewModel.Devices.Count == 0)
                                {
                                    <div class="p-4">
                                        <div class="barret-alert barret-alert-info">
                                            <div class="flex items-center">
                                                <i class="bi bi-info-circle text-blue-600 mr-2"></i>
                                                <span class="text-blue-800">No devices found in this vessel.</span>
                                            </div>
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    <RadzenDataGrid @ref="DeviceGrid"
                                                   Data="@vesselViewModel.Devices"
                                                   TItem="DeviceDto"
                                                   Class="barret-grid device-list-grid mb-0"
                                                   AllowFiltering="true"
                                                   AllowPaging="true"
                                                   PageSize="5"
                                                   RowSelect="@OnDeviceRowSelect">
                                        <Columns>
                                            <RadzenDataGridColumn TItem="DeviceDto" Title="" Width="50px" Sortable="false" Filterable="false">
                                                <Template Context="device">
                                                    @{
                                                        var deviceDto = device as DeviceDto;
                                                        if (deviceDto != null)
                                                        {
                                                            <BarretCheckBox Checked="@IsDeviceSelected(deviceDto.Id)"
                                                                          CheckedChanged="@((value) => { ToggleDeviceSelection(deviceDto.Id); StateHasChanged(); })" />
                                                        }
                                                    }
                                                </Template>
                                            </RadzenDataGridColumn>
                                            <RadzenDataGridColumn TItem="DeviceDto" Title="Name" Property="Name" Sortable="true" Filterable="true">
                                                <Template Context="device">
                                                    @{
                                                        var deviceDto = device as DeviceDto;
                                                        if (deviceDto != null)
                                                        {
                                                            <span class="@(IsDeviceSelected(deviceDto.Id) ? "font-bold text-blue-600" : "")">@deviceDto.Name</span>
                                                        }
                                                    }
                                                </Template>
                                            </RadzenDataGridColumn>
                                            <RadzenDataGridColumn TItem="DeviceDto" Title="Role" Property="DeviceRole" Sortable="true" Filterable="true" />
                                            <RadzenDataGridColumn TItem="DeviceDto" Title="Manufacturer" Property="ManufacturerName" Sortable="true" Filterable="true" />
                                            <RadzenDataGridColumn TItem="DeviceDto" Title="Model" Property="ModelName" Sortable="true" Filterable="true" />
                                            <RadzenDataGridColumn TItem="DeviceDto" Title="Group" Property="DeviceGroupName" Sortable="true" Filterable="true" />
                                        </Columns>
                                    </RadzenDataGrid>
                                }
                            }
                        }
                    </Template>
                </RadzenDataGrid>

                <div class="flex justify-between items-center mt-4">
                    <div>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                            @ViewModel.GetSelectedDeviceCount() devices selected
                        </span>
                    </div>
                </div>
            }
        </div>

        <div class="barret-dialog-footer">
            <div class="barret-dialog-btn-group-right">
                <RadzenButton Text="Cancel"
                             Icon="cancel"
                             ButtonStyle="ButtonStyle.Secondary"
                             Click="@CloseDialog"
                             class="barret-btn barret-form-btn" />
                <RadzenButton Text="Import Selected Devices"
                             Icon="download"
                             ButtonStyle="ButtonStyle.Primary"
                             Click="@ImportDevices"
                             Disabled="@(ViewModel.GetSelectedDeviceCount() == 0)"
                             class="barret-btn barret-form-btn" />
            </div>
        </div>
    </div>
</RadzenDialog>
